/**
 * 模态搜索页面管理类
 */
class ModalSearchManager {
    constructor() {
        // 左侧搜索相关元素
        this.vehicleSelect = document.getElementById('vehicle-select');
        this.componentSelect = document.getElementById('component-select');
        this.componentMultiselect = document.getElementById('component-multiselect');
        this.searchBtn = document.getElementById('search-btn');
        this.exportBtn = document.getElementById('export-btn');
        this.tableBody = document.getElementById('table-body');
        this.resultCount = document.getElementById('result-count');

        // 右侧对标相关元素
        this.comparisonComponents = document.getElementById('comparison-components');
        this.comparisonVehicles = document.getElementById('comparison-vehicles');
        this.comparisonConditions = document.getElementById('comparison-conditions');
        this.comparisonModes = document.getElementById('comparison-modes');
        this.comparisonBtn = document.getElementById('comparison-btn');
        this.comparisonExportBtn = document.getElementById('comparison-export-btn');

        // 车型选择相关元素
        this.vehicleSearch = document.getElementById('vehicle-search');

        // 自定义多选框相关元素
        this.comparisonVehiclesMultiselect = document.getElementById('comparison-vehicles-multiselect');
        this.comparisonConditionsMultiselect = document.getElementById('comparison-conditions-multiselect');
        this.comparisonModesMultiselect = document.getElementById('comparison-modes-multiselect');

        // 结果展示相关元素
        this.resultTitle = document.getElementById('result-title');
        this.currentExportBtn = document.getElementById('current-export-btn');
        this.modalSearchResults = document.getElementById('modal-search-results');
        this.comparisonResults = document.getElementById('comparison-results');

        // 对标结果展示元素
        this.comparisonTableContainer = document.getElementById('comparison-table-container');
        this.comparisonTable = document.getElementById('comparison-table');
        this.comparisonChartContainer = document.getElementById('comparison-chart-container');
        this.comparisonChart = document.getElementById('comparison-chart');
        this.comparisonEmpty = document.getElementById('comparison-empty');

        // 标签页相关元素
        this.modalSearchTab = document.getElementById('modal-search-tab');
        this.comparisonSearchTab = document.getElementById('comparison-search-tab');

        this.currentData = [];
        this.currentComparisonData = null;
        this.chartInstance = null;
        this.currentMode = 'modal'; // 'modal' 或 'comparison'

        // 多选框状态数据
        this.selectedVehicles = new Set();
        this.selectedConditions = new Set();
        this.selectedModes = new Set();
        this.selectedComponents = new Set(); // 新增：模态搜索面板的零件选择
        this.allVehicles = [];
        this.allConditions = [];
        this.allModes = [];
        this.allComponents = []; // 新增：模态搜索面板的零件列表
        this.filteredVehicles = [];

        // 车型标签选择相关数据
        this.allVehicles = [];
        this.filteredVehicles = [];
        this.selectedVehicles = new Set();

        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.loadVehicles();
        this.loadComparisonData();
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 左侧搜索事件
        this.vehicleSelect.addEventListener('change', () => {
            this.loadComponents();
        });

        this.searchBtn.addEventListener('click', () => {
            this.searchModalData();
        });

        this.exportBtn.addEventListener('click', () => {
            this.exportData();
        });

        // 统一导出按钮事件
        this.currentExportBtn.addEventListener('click', () => {
            if (this.currentMode === 'modal') {
                this.exportData();
            } else {
                this.exportComparisonData();
            }
        });

        // 对标事件
        this.comparisonBtn.addEventListener('click', () => {
            this.generateComparison();
        });

        this.comparisonExportBtn.addEventListener('click', () => {
            this.exportComparisonData();
        });

        // 零件选择变化事件（级联筛选）
        this.comparisonComponents.addEventListener('change', () => {
            this.onComponentChange();
        });

        // 车型搜索事件
        this.vehicleSearch.addEventListener('input', (e) => {
            this.filterVehicles(e.target.value);
        });

        // 自定义多选框事件绑定
        this.initMultiselect(this.componentMultiselect, 'components'); // 新增：模态搜索面板的零件多选
        this.initMultiselect(this.comparisonVehiclesMultiselect, 'vehicles');
        this.initMultiselect(this.comparisonConditionsMultiselect, 'conditions');
        this.initMultiselect(this.comparisonModesMultiselect, 'modes');

        // 原生多选框事件（保留用于兼容）
        this.comparisonConditions.addEventListener('change', () => {
            // 兼容性事件，新的多选框会自动更新显示
        });

        this.comparisonModes.addEventListener('change', () => {
            // 兼容性事件，新的多选框会自动更新显示
        });

        // 标签页切换事件
        document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tabType = e.currentTarget.getAttribute('data-tab');
            this.switchTab(tabType);
        });
    });
    }
    /**
     * 切换标签页
     */
    switchTab(tabType) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

        // 更新内容面板显示
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });

        if (tabType === 'modal-search') {
            document.getElementById('modal-search-pane').classList.add('show', 'active');
            this.switchToModalMode();
        } else if (tabType === 'comparison-search') {
            document.getElementById('comparison-search-pane').classList.add('show', 'active');
            this.switchToComparisonMode();
        }
    }

    /**
     * 加载车型列表
     */
    async loadVehicles() {
        try {
            const result = await request.get('/modal/api/vehicles');
            this.renderVehicleOptions(result.data);
        } catch (error) {
            showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载零部件列表
     */
    async loadComponents() {
        const vehicleId = this.vehicleSelect.value;
        if (!vehicleId) {
            // 清空零件选择
            this.componentSelect.innerHTML = '<option value="">全部零件</option>';
            this.clearMultiselect('components');
            this.setMultiselectLoading('components', '请先选择车型');
            return;
        }

        try {
            // 显示加载状态
            this.setMultiselectLoading('components', '加载中...');

            const result = await request.get('/modal/api/components', { vehicle_id: vehicleId });
            this.renderComponentOptions(result.data);
        } catch (error) {
            this.setMultiselectLoading('components', '加载失败');
            showMessage('加载零部件列表失败: ' + error.message, 'error');
        }
    }

    /**
     * 搜索模态数据
     */
    async searchModalData() {
        const selectedComponentIds = Array.from(this.selectedComponents);
        const params = {
            vehicle_model_id: this.vehicleSelect.value,
            component_ids: selectedComponentIds.length > 0 ? selectedComponentIds : []
        };

        if (!params.vehicle_model_id) {
            showMessage('请选择车型', 'warning');
            return;
        }

        // 显示加载状态
        this.showTableLoading();
        this.searchBtn.disabled = true;

        try {
            // 使用URLSearchParams来正确处理数组参数
            const urlParams = new URLSearchParams();
            urlParams.append('vehicle_model_id', params.vehicle_model_id);
            params.component_ids.forEach(id => urlParams.append('component_ids', id));

            const result = await request.get('/modal/api/search?' + urlParams.toString());
            this.currentData = result.data;
            this.renderTable(result.data);
            this.updateResultCount(result.data.length);
            this.exportBtn.disabled = result.data.length === 0;

            // 自动切换到模态搜索结果显示
            this.switchToModalMode();
            showMessage('查询成功', 'success');
        } catch (error) {
            this.showTableError('查询失败: ' + error.message);
            showMessage('查询失败: ' + error.message, 'error');
        } finally {
            this.searchBtn.disabled = false;
        }
    }

    /**
     * 渲染车型选项
     */
    renderVehicleOptions(vehicles) {
        this.vehicleSelect.innerHTML = '<option value="">请选择车型</option>';
        vehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = vehicle.name;
            this.vehicleSelect.appendChild(option);
        });
    }

    /**
     * 渲染零部件选项
     */
    renderComponentOptions(components) {
        // 更新隐藏的select元素
        this.componentSelect.innerHTML = '<option value="">全部零件</option>';
        components.forEach(component => {
            const option = document.createElement('option');
            option.value = component.id;
            option.textContent = component.name;
            this.componentSelect.appendChild(option);
        });

        // 更新多选框数据和显示
        this.allComponents = components;
        this.selectedComponents.clear();
        this.renderMultiselectOptions(this.componentMultiselect, components, 'components');
        this.updateMultiselectDisplay(this.componentMultiselect, 'components');
    }

    /**
     * 渲染数据表格
     */
    renderTable(dataList) {
        if (dataList.length === 0) {
            this.showTableEmpty();
            return;
        }
        
        this.tableBody.innerHTML = '';
        
        dataList.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.category}</td>
                <td>${item.sub_category}</td>
                <td>${item.component_name}</td>
                <td class="frequency-value">${formatNumber(item.frequency, 1)}</td>
                <td>${item.mode_shape_description || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-primary btn-action" onclick="modalSearchManager.showModeShape(${item.id})">
                        <i class="fas fa-wave-square me-1"></i>查看振型
                    </button>
                </td>
            `;
            this.tableBody.appendChild(row);
        });
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="loading-spinner mb-2"></div>
                    <div class="text-muted">正在搜索数据...</div>
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格空状态
     */
    showTableEmpty() {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    没有找到符合条件的数据
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格错误状态
     */
    showTableError(message) {
        this.tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                    ${message}
                </td>
            </tr>
        `;
    }

    /**
     * 更新结果计数
     */
    updateResultCount(count) {
        this.resultCount.textContent = `${count} 条记录`;
    }

    /**
     * 显示振型
     */
    async showModeShape(dataId) {
        try {
            const result = await request.get(`/modal/api/data/${dataId}`);
            this.renderModeShapeModal(result.data);

            const modal = new bootstrap.Modal(document.getElementById('modal-detail-modal'));
            modal.show();
        } catch (error) {
            showMessage('获取振型数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 渲染振型查看弹窗
     */
    renderModeShapeModal(data) {
        const content = document.getElementById('modal-detail-content');

        content.innerHTML = `
            <!-- 基本信息行 -->
            <div class="mode-shape-info-row">
                <div class="info-item">
                    <span class="info-label">车型:</span>
                    <span class="info-value">${data.vehicle_info.vehicle_model_name}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">零件:</span>
                    <span class="info-value">${data.component_info ? data.component_info.component_name : '-'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">频率:</span>
                    <span class="info-value">${formatNumber(data.frequency, 1)} Hz</span>
                </div>
                <div class="info-item">
                    <span class="info-label">模态类型:</span>
                    <span class="info-value">${data.mode_shape_description || '-'}</span>
                </div>
            </div>

            <!-- 图片展示区域 -->
            <div class="mode-shape-tabs">
                <div class="tabs-header">
                    <button class="tab-button active" data-target="shape-pane">
                        振型动画
                    </button>
                    <button class="tab-button" data-target="photo-pane">
                        测试照片
                    </button>
                </div>
                <div class="tab-content-area">
                    <div class="tab-pane active" id="shape-pane">
                        <div class="image-container">
                            ${data.mode_shape_file ? `
                                <img src="${data.mode_shape_file}" alt="振型动画" class="mode-shape-image">
                            ` : `
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无振型动画</p>
                                </div>
                            `}
                        </div>
                    </div>
                    <div class="tab-pane" id="photo-pane">
                        <div class="image-container">
                            ${data.test_photo_file ? `
                                <img src="${data.test_photo_file}" alt="测试照片" class="mode-shape-image">
                            ` : `
                                <div class="no-image-placeholder">
                                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无测试照片</p>
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化标签页切换功能
        this.initTabSwitching();
    }

    /**
     * 初始化标签页切换功能
     */
    initTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');

                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // 添加当前活动状态
                button.classList.add('active');
                document.getElementById(targetId).classList.add('active');
            });
        });
    }

    /**
     * 导出数据
     */
    exportData() {
        if (this.currentData.length === 0) {
            showMessage('没有数据可导出', 'warning');
            return;
        }
        
        // 简单的CSV导出
        const headers = ['分类', '子分类', '零件名称', '频率(Hz)', '阶次', '模态描述'];
        const csvContent = [
            headers.join(','),
            ...this.currentData.map(item => [
                item.category,
                item.sub_category,
                item.component_name,
                item.frequency,
                item.mode_order,
                item.mode_shape_description || ''
            ].join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `模态数据_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();
        
        showMessage('数据导出成功', 'success');
    }

    /**
     * 切换到模态数据搜索模式
     */
    switchToModalMode() {
        this.currentMode = 'modal';
        this.resultTitle.innerHTML = '<i class="fas fa-table me-2"></i>搜索结果';
        this.modalSearchResults.style.display = 'block';
        this.comparisonResults.style.display = 'none';
        this.updateExportButton();
    }

    /**
     * 切换到对标搜索模式
     */
    switchToComparisonMode() {
        this.currentMode = 'comparison';
        this.resultTitle.innerHTML = '<i class="fas fa-chart-bar me-2"></i>对标结果';
        this.modalSearchResults.style.display = 'none';
        this.comparisonResults.style.display = 'block';
        this.updateExportButton();
    }

    /**
     * 更新导出按钮状态
     */
    updateExportButton() {
        if (this.currentMode === 'modal') {
            this.currentExportBtn.disabled = this.currentData.length === 0;
            this.currentExportBtn.innerHTML = '<i class="fas fa-download me-1"></i>导出搜索结果';
        } else {
            this.currentExportBtn.disabled = !this.currentComparisonData || !this.currentComparisonData.chart_data;
            this.currentExportBtn.innerHTML = '<i class="fas fa-download me-1"></i>导出对标结果';
        }
    }

    /**
     * 加载对标功能所需的数据
     */
    async loadComparisonData() {
        try {
            // 加载车型列表（对标用）
            const vehiclesResult = await request.get('/modal/api/vehicles');
            this.renderComparisonVehicleOptions(vehiclesResult.data);

            // 加载零件列表（对标用）
            const componentsResult = await request.get('/modal/api/components');
            this.renderComparisonComponentOptions(componentsResult.data);

            // 初始化测试状态和模态振型为空（等待选择零件后加载）
            this.comparisonConditions.innerHTML = '<option value="">请先选择零件</option>';
            this.comparisonModes.innerHTML = '<option value="">请先选择零件</option>';

            // 初始化自定义多选框状态
            this.clearMultiselect('vehicles');
            this.clearMultiselect('conditions');
            this.clearMultiselect('modes');
        } catch (error) {
            showMessage('加载对标数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 渲染对标车型选项
     */
    renderComparisonVehicleOptions(vehicles) {
        this.allVehicles = vehicles;
        this.filteredVehicles = vehicles;

        // 渲染到新的多选框
        this.renderMultiselectOptions(this.comparisonVehiclesMultiselect, vehicles, 'vehicles');
        this.updateMultiselectDisplay(this.comparisonVehiclesMultiselect, 'vehicles');

        // 同时更新隐藏的select元素（用于表单提交）
        this.comparisonVehicles.innerHTML = '';
        vehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = vehicle.name;
            this.comparisonVehicles.appendChild(option);
        });
    }

    /**
     * 渲染对标零件选项
     */
    renderComparisonComponentOptions(components) {
        this.comparisonComponents.innerHTML = '<option value="">请选择零件</option>';
        components.forEach(component => {
            const option = document.createElement('option');
            option.value = component.id;
            option.textContent = component.name;
            this.comparisonComponents.appendChild(option);
        });
    }



    /**
     * 渲染对标模态振型选项
     */
    renderComparisonModeOptions(modes) {
        this.comparisonModes.innerHTML = '';
        modes.forEach(mode => {
            const option = document.createElement('option');
            option.value = mode.value;
            option.textContent = mode.label;
            this.comparisonModes.appendChild(option);
        });

    }

    /**
     * 过滤车型
     */
    filterVehicles(searchText) {
        if (!searchText.trim()) {
            this.filteredVehicles = this.allVehicles;
        } else {
            this.filteredVehicles = this.allVehicles.filter(vehicle =>
                vehicle.name.toLowerCase().includes(searchText.toLowerCase())
            );
        }
        // 重新渲染过滤后的车型选项
        this.renderMultiselectOptions(this.comparisonVehiclesMultiselect, this.filteredVehicles, 'vehicles');
    }



    /**
     * 初始化自定义多选框
     */
    initMultiselect(container, type) {
        const trigger = container.querySelector('.multiselect-trigger');

        // 点击触发器展开/折叠
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleMultiselect(container);
        });

        // 特殊处理车型多选框的搜索功能
        if (type === 'vehicles') {
            const searchInput = container.querySelector('#vehicle-search');
            if (searchInput) {
                searchInput.addEventListener('click', (e) => {
                    e.stopPropagation(); // 防止点击搜索框时关闭下拉框
                });
            }
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                this.closeMultiselect(container);
            }
        });
    }

    /**
     * 切换多选框展开/折叠状态
     */
    toggleMultiselect(container) {
        const dropdown = container.querySelector('.multiselect-dropdown');
        const arrow = container.querySelector('.multiselect-arrow');
        const trigger = container.querySelector('.multiselect-trigger');

        if (dropdown.classList.contains('show')) {
            this.closeMultiselect(container);
        } else {
            // 关闭其他多选框
            document.querySelectorAll('.custom-multiselect .multiselect-dropdown.show').forEach(dd => {
                dd.classList.remove('show');
                dd.parentElement.querySelector('.multiselect-arrow').classList.remove('rotated');
                dd.parentElement.querySelector('.multiselect-trigger').classList.remove('active');
            });

            // 打开当前多选框
            dropdown.classList.add('show');
            arrow.classList.add('rotated');
            trigger.classList.add('active');
        }
    }

    /**
     * 关闭多选框
     */
    closeMultiselect(container) {
        const dropdown = container.querySelector('.multiselect-dropdown');
        const arrow = container.querySelector('.multiselect-arrow');
        const trigger = container.querySelector('.multiselect-trigger');

        dropdown.classList.remove('show');
        arrow.classList.remove('rotated');
        trigger.classList.remove('active');
    }

    /**
     * 渲染多选框选项
     */
    renderMultiselectOptions(container, options, type) {
        let targetContainer;

        if (type === 'vehicles') {
            // 车型选项渲染到特定容器
            targetContainer = container.querySelector('#vehicle-options-container');
        } else {
            // 其他选项直接渲染到下拉框
            targetContainer = container.querySelector('.multiselect-dropdown');
        }

        if (!options || options.length === 0) {
            targetContainer.innerHTML = '<div class="multiselect-empty">暂无选项</div>';
            return;
        }

        targetContainer.innerHTML = '';
        options.forEach(option => {
            const optionElement = document.createElement('div');
            optionElement.className = 'multiselect-option';
            optionElement.dataset.value = option.value || option.id;

            let selectedSet;
            if (type === 'vehicles') {
                selectedSet = this.selectedVehicles;
            } else if (type === 'conditions') {
                selectedSet = this.selectedConditions;
            } else if (type === 'components') {
                selectedSet = this.selectedComponents;
            } else {
                selectedSet = this.selectedModes;
            }

            const isSelected = selectedSet.has(option.value || option.id);

            if (isSelected) {
                optionElement.classList.add('selected');
            }

            optionElement.innerHTML = `
                <i class="option-checkbox ${isSelected ? 'fas fa-check-square' : 'far fa-square'}"></i>
                <span class="option-text">${option.label || option.name}</span>
            `;

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleOption(container, option.value || option.id, type);
            });

            targetContainer.appendChild(optionElement);
        });
    }

    /**
     * 切换选项选择状态
     */
    toggleOption(container, value, type) {
        let selectedSet, hiddenSelect, options;

        if (type === 'vehicles') {
            selectedSet = this.selectedVehicles;
            hiddenSelect = this.comparisonVehicles;
            options = this.filteredVehicles;
        } else if (type === 'conditions') {
            selectedSet = this.selectedConditions;
            hiddenSelect = this.comparisonConditions;
            options = this.allConditions;
        } else if (type === 'components') {
            selectedSet = this.selectedComponents;
            hiddenSelect = this.componentSelect;
            options = this.allComponents;
        } else {
            selectedSet = this.selectedModes;
            hiddenSelect = this.comparisonModes;
            options = this.allModes;
        }

        if (selectedSet.has(value)) {
            selectedSet.delete(value);
        } else {
            selectedSet.add(value);
        }

        // 更新隐藏的select元素
        this.updateHiddenSelect(hiddenSelect, selectedSet);

        // 更新显示
        this.updateMultiselectDisplay(container, type);

        // 重新渲染选项以更新选择状态
        this.renderMultiselectOptions(container, options, type);
    }

    /**
     * 更新多选框显示
     */
    updateMultiselectDisplay(container, type) {
        const placeholder = container.querySelector('.multiselect-placeholder');
        const count = container.querySelector('.multiselect-count');

        let selectedSet;
        if (type === 'vehicles') {
            selectedSet = this.selectedVehicles;
        } else if (type === 'conditions') {
            selectedSet = this.selectedConditions;
        } else if (type === 'components') {
            selectedSet = this.selectedComponents;
        } else {
            selectedSet = this.selectedModes;
        }

        const selectedCount = selectedSet.size;
        count.textContent = `已选: ${selectedCount}`;

        if (selectedCount > 0) {
            placeholder.classList.add('has-selection');
        } else {
            placeholder.classList.remove('has-selection');
        }
    }

    /**
     * 更新隐藏的select元素
     */
    updateHiddenSelect(selectElement, selectedSet) {
        // 清除所有选择
        Array.from(selectElement.options).forEach(option => {
            option.selected = false;
        });

        // 设置新的选择
        selectedSet.forEach(value => {
            const option = selectElement.querySelector(`option[value="${value}"]`);
            if (option) {
                option.selected = true;
            }
        });

        // 触发change事件以保持兼容性
        selectElement.dispatchEvent(new Event('change'));
    }

    /**
     * 零件选择变化处理（级联筛选）
     */
    async onComponentChange() {
        const componentId = this.comparisonComponents.value;

        if (!componentId) {
            // 如果没有选择零件，清空其他选项
            this.comparisonConditions.innerHTML = '<option value="">请选择测试状态</option>';
            this.comparisonModes.innerHTML = '<option value="">请选择模态振型</option>';

            // 清空自定义多选框
            this.clearMultiselect('conditions');
            this.clearMultiselect('modes');
            return;
        }

        try {
            // 显示加载状态
            this.comparisonConditions.innerHTML = '<option value="">加载中...</option>';
            this.comparisonModes.innerHTML = '<option value="">加载中...</option>';

            // 更新自定义多选框加载状态
            this.setMultiselectLoading('conditions', '加载中...');
            this.setMultiselectLoading('modes', '加载中...');

            // 获取该零件对应的测试状态和模态振型
            const result = await request.get(`/modal/api/component-options/${componentId}`);

            // 更新测试状态选项
            this.comparisonConditions.innerHTML = '<option value="">请选择测试状态</option>';
            result.data.test_conditions.forEach(condition => {
                const option = document.createElement('option');
                option.value = condition.value;
                option.textContent = condition.label;
                this.comparisonConditions.appendChild(option);
            });

            // 更新模态振型选项
            this.comparisonModes.innerHTML = '<option value="">请选择模态振型</option>';
            result.data.mode_shapes.forEach(mode => {
                const option = document.createElement('option');
                option.value = mode.value;
                option.textContent = mode.label;
                this.comparisonModes.appendChild(option);
            });

            // 更新自定义多选框选项
            this.allConditions = result.data.test_conditions;
            this.allModes = result.data.mode_shapes;
            this.selectedConditions.clear();
            this.selectedModes.clear();

            this.renderMultiselectOptions(this.comparisonConditionsMultiselect, this.allConditions, 'conditions');
            this.renderMultiselectOptions(this.comparisonModesMultiselect, this.allModes, 'modes');
            this.updateMultiselectDisplay(this.comparisonConditionsMultiselect, 'conditions');
            this.updateMultiselectDisplay(this.comparisonModesMultiselect, 'modes');

            // 多选框会自动更新显示

            // 显示提示信息
            showMessage(`该零件共有 ${result.data.test_conditions.length} 种测试状态，${result.data.mode_shapes.length} 个模态振型`, 'info');

        } catch (error) {
            this.comparisonConditions.innerHTML = '<option value="">加载失败</option>';
            this.comparisonModes.innerHTML = '<option value="">加载失败</option>';

            // 更新自定义多选框错误状态
            this.setMultiselectLoading('conditions', '加载失败');
            this.setMultiselectLoading('modes', '加载失败');

            showMessage('加载零件选项失败: ' + error.message, 'error');
        }
    }

    /**
     * 清空多选框
     */
    clearMultiselect(type) {
        let container, selectedSet;

        if (type === 'vehicles') {
            container = this.comparisonVehiclesMultiselect;
            selectedSet = this.selectedVehicles;
        } else if (type === 'conditions') {
            container = this.comparisonConditionsMultiselect;
            selectedSet = this.selectedConditions;
        } else if (type === 'components') {
            container = this.componentMultiselect;
            selectedSet = this.selectedComponents;
        } else {
            container = this.comparisonModesMultiselect;
            selectedSet = this.selectedModes;
        }

        selectedSet.clear();
        this.updateMultiselectDisplay(container, type);

        if (type === 'vehicles') {
            const optionsContainer = container.querySelector('#vehicle-options-container');
            optionsContainer.innerHTML = '<div class="multiselect-loading">加载中...</div>';
        } else if (type === 'components') {
            const dropdown = container.querySelector('.multiselect-dropdown');
            dropdown.innerHTML = '<div class="multiselect-loading">请先选择车型</div>';
        } else {
            const dropdown = container.querySelector('.multiselect-dropdown');
            dropdown.innerHTML = '<div class="multiselect-loading">请先选择零件</div>';
        }
    }

    /**
     * 设置多选框加载状态
     */
    setMultiselectLoading(type, message) {
        let container;

        if (type === 'vehicles') {
            container = this.comparisonVehiclesMultiselect;
            const optionsContainer = container.querySelector('#vehicle-options-container');
            optionsContainer.innerHTML = `<div class="multiselect-loading">${message}</div>`;
        } else if (type === 'conditions') {
            container = this.comparisonConditionsMultiselect;
            const dropdown = container.querySelector('.multiselect-dropdown');
            dropdown.innerHTML = `<div class="multiselect-loading">${message}</div>`;
        } else if (type === 'components') {
            container = this.componentMultiselect;
            const dropdown = container.querySelector('.multiselect-dropdown');
            dropdown.innerHTML = `<div class="multiselect-loading">${message}</div>`;
        } else {
            container = this.comparisonModesMultiselect;
            const dropdown = container.querySelector('.multiselect-dropdown');
            dropdown.innerHTML = `<div class="multiselect-loading">${message}</div>`;
        }
    }

    /**
     * 生成对标数据
     */
    async generateComparison() {
        const params = {
            component_ids: this.comparisonComponents.value ? [this.comparisonComponents.value] : [],
            vehicle_ids: Array.from(this.selectedVehicles),
            test_conditions: Array.from(this.selectedConditions),
            mode_shapes: Array.from(this.selectedModes)
        };

        // 参数验证
        if (params.component_ids.length === 0) {
            showMessage('请选择零件', 'warning');
            return;
        }
        if (params.vehicle_ids.length === 0) {
            showMessage('请选择车型', 'warning');
            return;
        }
        if (params.test_conditions.length === 0) {
            showMessage('请选择测试状态', 'warning');
            return;
        }
        if (params.mode_shapes.length === 0) {
            showMessage('请选择模态振型', 'warning');
            return;
        }

        // 显示加载状态
        this.showComparisonLoading();
        this.comparisonBtn.disabled = true;

        try {
            // 使用URLSearchParams来正确处理数组参数
            const urlParams = new URLSearchParams();

            // 添加数组参数
            params.component_ids.forEach(id => urlParams.append('component_ids', id));
            params.vehicle_ids.forEach(id => urlParams.append('vehicle_ids', id));
            params.test_conditions.forEach(condition => urlParams.append('test_conditions', condition));
            params.mode_shapes.forEach(mode => urlParams.append('mode_shapes', mode));
            const result = await request.get('/modal/api/comparison?' + urlParams.toString());

//            const result = await request.get('/modal/api/comparison', params);
            this.currentComparisonData = result.data;
            this.renderComparisonResults(result.data);
            this.comparisonExportBtn.disabled = false;

            // 自动切换到对标结果显示
            this.switchToComparisonMode();
            showMessage('对标数据生成成功', 'success');
        } catch (error) {
            this.showComparisonError('生成对标失败: ' + error.message);
            showMessage('生成对标失败: ' + error.message, 'error');
        } finally {
            this.comparisonBtn.disabled = false;
        }
    }

    /**
     * 显示对标加载状态
     */
    showComparisonLoading() {
        this.comparisonEmpty.innerHTML = `
            <div class="text-center py-4">
                <div class="loading-spinner mb-2"></div>
                <div class="text-muted">正在生成对标数据...</div>
            </div>
        `;
        this.comparisonTableContainer.style.display = 'none';
        this.comparisonChartContainer.style.display = 'none';
        this.comparisonEmpty.style.display = 'block';
    }

    /**
     * 显示对标错误状态
     */
    showComparisonError(message) {
        this.comparisonEmpty.innerHTML = `
            <div class="text-center text-danger py-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br>
                ${message}
            </div>
        `;
        this.comparisonTableContainer.style.display = 'none';
        this.comparisonChartContainer.style.display = 'none';
        this.comparisonEmpty.style.display = 'block';
    }

    /**
     * 渲染对标结果
     */
    renderComparisonResults(data) {
        if (!data.comparison_table || !data.chart_data || data.chart_data.length === 0) {
            this.comparisonEmpty.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    没有找到符合条件的对标数据
                </div>
            `;
            this.comparisonTableContainer.style.display = 'none';
            this.comparisonChartContainer.style.display = 'none';
            this.comparisonEmpty.style.display = 'block';
            return;
        }

        // 隐藏空状态，显示结果
        this.comparisonEmpty.style.display = 'none';
        this.comparisonTableContainer.style.display = 'block';
        this.comparisonChartContainer.style.display = 'block';

        // 渲染表格
        this.renderComparisonTable(data.comparison_table);

        // 渲染图表
        this.renderComparisonChart(data.chart_data);
    }

    /**
     * 渲染对标表格
     */
    renderComparisonTable(tableData) {
        const thead = this.comparisonTable.querySelector('thead');
        const tbody = this.comparisonTable.querySelector('tbody');

        // 生成表头
        thead.innerHTML = `
            <tr>
                ${tableData.headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
        `;

        // 生成表格内容
        tbody.innerHTML = '';
        tableData.rows.forEach(row => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td><strong>${row.mode_shape}</strong></td>
                ${tableData.headers.slice(1).map(header => {
                    const value = row[header];
                    return `<td>${value === '-' ? '-' : formatNumber(value, 1)}</td>`;
                }).join('')}
            `;
            tbody.appendChild(tr);
        });
    }

    /**
     * 渲染对标图表
     */
    renderComparisonChart(chartData) {
        // 销毁之前的图表实例
        if (this.chartInstance) {
            this.chartInstance.destroy();
        }

        // 准备图表数据
        const datasets = this.prepareChartDatasets(chartData);
        const labels = [...new Set(chartData.map(item => item.vehicle_condition))];

        const ctx = this.comparisonChart.getContext('2d');
        this.chartInstance = new Chart(ctx, {
            type: 'scatter',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false, // 允许图表填充容器
                font: {
                    family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                },
                plugins: {
                    title: {
                        display: true,
                        text: '模态频率对比图',
                        font: {
                            size: 20,        // 标题字体大小 (可调整: 16-24)
                            weight: 'bold',  // 标题字体加粗
                            family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 14,        // 图例字体大小 (可调整: 12-16)
                                weight: '600',   // 图例字体加粗
                                family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                            },
                            padding: 20
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'category',
                        title: {
                            display: true,
                            text: '车型_测试状态',
                            font: {
                                size: 18,        // X轴标题字体大小 (可调整: 14-18)
                                weight: 'bold',  // X轴标题字体加粗
                                family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                            }
                        },
                        ticks: {
                            font: {
                                size: 14,        // X轴刻度字体大小 (可调整: 11-15)
                                weight: '500',   // X轴刻度字体加粗
                                family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '频率 (Hz)',
                            font: {
                                size: 16,        // Y轴标题字体大小 (可调整: 14-18)
                                weight: 'bold',  // Y轴标题字体加粗
                                family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                            }
                        },
                        ticks: {
                            font: {
                                size: 14,        // Y轴刻度字体大小 (可调整: 11-15)
                                weight: '500',   // Y轴刻度字体加粗
                                family: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif"
                            }
                        }
                    }
                },
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const elementIndex = elements[0].index;
                        const datasetIndex = elements[0].datasetIndex;
                        const clickedData = this.getClickedChartData(datasetIndex, elementIndex);
                        if (clickedData) {
                            this.showModeShapeFromChart(clickedData);
                        }
                    }
                }
            }
        });
    }

    /**
     * 准备图表数据集
     */
    prepareChartDatasets(chartData) {
        const modeShapes = [...new Set(chartData.map(item => item.mode_shape))];
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];

        return modeShapes.map((modeShape, index) => {
            const modeData = chartData.filter(item => item.mode_shape === modeShape);
            const data = modeData.map(item => ({
                x: item.vehicle_condition,
                y: item.frequency
            }));

            return {
                label: modeShape,
                data: data,
                backgroundColor: colors[index % colors.length],
                borderColor: colors[index % colors.length],
                pointRadius: 8,           // 散点大小 (可调整: 6-12)
                pointHoverRadius: 12,     // 鼠标悬停时散点大小 (可调整: 8-16)
                borderWidth: 2            // 散点边框宽度 (可调整: 1-3)
            };
        });
    }

    /**
     * 导出对标数据
     */
    exportComparisonData() {
        if (!this.currentComparisonData || !this.currentComparisonData.chart_data) {
            showMessage('没有对标数据可导出', 'warning');
            return;
        }

        const chartData = this.currentComparisonData.chart_data;
        const headers = ['车型_测试状态', '模态振型', '频率(Hz)', '车型名称', '测试状态', '零件名称'];
        const csvContent = [
            headers.join(','),
            ...chartData.map(item => [
                item.vehicle_condition,
                item.mode_shape,
                item.frequency,
                item.vehicle_name,
                item.test_condition,
                item.component_name
            ].join(','))
        ].join('\n');

        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `模态对标数据_${new Date().toISOString().slice(0, 10)}.csv`;
        link.click();

        showMessage('对标数据导出成功', 'success');
    }

    /**
     * 获取点击的图表数据
     */
    getClickedChartData(datasetIndex, elementIndex) {
        if (!this.currentComparisonData || !this.currentComparisonData.chart_data) {
            return null;
        }

        const datasets = this.prepareChartDatasets(this.currentComparisonData.chart_data);
        const dataset = datasets[datasetIndex];
        const modeShape = dataset.label;
        const dataPoint = dataset.data[elementIndex];

        // 在原始数据中查找对应的数据项
        const matchedData = this.currentComparisonData.chart_data.find(item =>
            item.mode_shape === modeShape &&
            item.vehicle_condition === dataPoint.x &&
            item.frequency === dataPoint.y
        );

        return matchedData;
    }

    /**
     * 从图表点击显示振型
     */
    async showModeShapeFromChart(chartData) {
        try {
            // 需要根据图表数据查找对应的模态数据ID
            // 这里需要扩展后端API来支持根据条件查找模态数据
            const params = {
                vehicle_condition: chartData.vehicle_condition,
                mode_shape: chartData.mode_shape,
                frequency: chartData.frequency,
                component_name: chartData.component_name
            };

            const result = await request.get('/modal/api/find-modal-data', params);
            if (result.data && result.data.id) {
                this.showModeShape(result.data.id);
            } else {
                showMessage('未找到对应的模态数据详情', 'warning');
            }
        } catch (error) {
            showMessage('获取振型数据失败: ' + error.message, 'error');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.modalSearchManager = new ModalSearchManager();
});
